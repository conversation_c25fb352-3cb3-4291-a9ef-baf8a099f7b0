# LVGL手表模拟器运行指南

这个指南将帮助您在PC上运行LVGL手表项目的模拟器版本。

## 🚀 快速开始

### 1. 安装依赖

#### Ubuntu/Debian 系统:
```bash
sudo apt-get update
sudo apt-get install libsdl2-dev build-essential
```

#### CentOS/RHEL 系统:
```bash
sudo yum install SDL2-devel gcc make
```

#### macOS 系统:
```bash
# 首先安装Homebrew (如果未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装SDL2
brew install sdl2
```

#### Windows 系统:
Windows用户建议使用以下方式之一:

**方式1: 使用MinGW-w64 + MSYS2**
1. 下载安装 [MSYS2](https://www.msys2.org/)
2. 在MSYS2终端中运行:
```bash
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-SDL2
pacman -S make
```

**方式2: 使用Visual Studio** 
1. 下载SDL2开发库：https://www.libsdl.org/download-2.0.php
2. 解压到项目目录，配置包含路径和库路径

### 2. 构建和运行

#### 方式1: 使用自动构建脚本 (推荐)
```bash
# 赋予执行权限
chmod +x build_simulator.sh

# 运行构建脚本
./build_simulator.sh
```

#### 方式2: 手动构建
```bash
# 使用简化的Makefile
make -f Makefile.simple clean
make -f Makefile.simple

# 运行模拟器
./lvgl_simulator
```

## 🎮 操作说明

- **鼠标左键**: 模拟触摸屏点击
- **鼠标移动**: 模拟触摸滑动
- **关闭窗口**: 退出模拟器

## 📁 文件结构说明

```
项目根目录/
├── lvgl/                    # LVGL库源代码
├── lvgl_user/              # 用户界面代码
│   ├── ui_app/            # SquareLine Studio生成的UI
│   │   ├── ui.c           # 主UI文件
│   │   ├── screens/       # 屏幕定义
│   │   └── fonts/         # 自定义字体
├── simulator_main.c        # 模拟器主程序
├── lv_conf_simulator.h     # 模拟器专用配置
├── build_simulator.sh      # 自动构建脚本
└── Makefile.simple        # 简化的Makefile
```

## ⚙️ 配置说明

### 模拟器窗口设置
在 `simulator_main.c` 中可以修改:
```c
#define WINDOW_WIDTH  240   // 窗口宽度
#define WINDOW_HEIGHT 240   // 窗口高度
```

### LVGL配置
模拟器使用专门的配置文件 `lv_conf_simulator.h`，主要特点:
- 16位颜色深度 (RGB565)
- 使用系统malloc/free
- 启用性能监视器
- 针对PC优化的刷新率

## 🐛 常见问题解决

### 问题1: SDL2未找到
**错误信息**: `fatal error: SDL2/SDL.h: No such file or directory`

**解决方案**:
- Linux: `sudo apt-get install libsdl2-dev`
- macOS: `brew install sdl2`
- Windows: 确保SDL2开发库已正确安装和配置

### 问题2: 编译错误
**错误信息**: 各种编译错误

**解决方案**:
1. 确保所有源文件存在
2. 检查文件路径是否正确
3. 尝试清理后重新编译: `make clean && make`

### 问题3: 窗口显示异常
**现象**: 窗口黑屏或显示错误

**解决方案**:
1. 检查显卡驱动是否支持OpenGL
2. 尝试不同的SDL渲染器
3. 检查颜色格式设置

### 问题4: 中文字体显示异常
**现象**: 中文字符显示为方块

**解决方案**:
1. 确保使用了支持中文的字体文件
2. 检查字体文件路径是否正确
3. 验证字体编码设置

## 🔧 开发提示

### 添加新的UI屏幕
1. 在SquareLine Studio中设计新屏幕
2. 导出代码到 `lvgl_user/ui_app/screens/`
3. 在 `ui.h` 中声明新屏幕
4. 重新编译模拟器

### 修改传感器数据
在 `simulator_main.c` 中找到模拟数据部分:
```c
// 更新传感器数据（模拟变化）
static uint32_t sensor_timer = 0;
if (current_tick - sensor_timer > 5000) {
    sensor_timer = current_tick;
    csi_sensor.dose_rate += (rand() % 100 - 50) * 0.01f;
    // 在这里添加更多传感器数据模拟
}
```

### 调试技巧
1. 启用LVGL日志: 在 `lv_conf_simulator.h` 中设置 `LV_LOG_LEVEL`
2. 使用性能监视器: `LV_USE_PERF_MONITOR 1`
3. 添加printf调试信息到关键函数

## 📊 性能优化

### 提高帧率
- 减少 `LV_DISP_DEF_REFR_PERIOD` 值
- 使用硬件加速 (如果支持)
- 优化绘制缓冲区大小

### 减少内存使用
- 调整 `LV_MEM_SIZE` 大小
- 减少同时显示的对象数量
- 优化图片和字体资源

## 📝 更新日志

- **v1.0** - 基础模拟器功能
- **v1.1** - 添加自动构建脚本
- **v1.2** - 优化SDL渲染性能
- **v1.3** - 支持更多平台

## 🤝 贡献

欢迎提交问题报告和改进建议！

## 📄 许可证

本项目遵循原LVGL项目的MIT许可证。 