@echo off
chcp 65001 >nul
echo ========================================
echo       自动安装SDL2并运行LVGL模拟器
echo ========================================
echo.

:: 设置SDL2版本和下载URL
set "SDL2_VERSION=2.28.5"
set "SDL2_URL=https://github.com/libsdl-org/SDL/releases/download/release-%SDL2_VERSION%/SDL2-devel-%SDL2_VERSION%-mingw.zip"
set "SDL2_DIR=C:\SDL2"
set "TEMP_DIR=%TEMP%\SDL2_Download"

:: 检查是否已安装
if exist "%SDL2_DIR%\bin\SDL2.dll" (
    echo ✅ SDL2已安装，跳过下载
    goto :configure_build
)

echo 🔄 开始下载SDL2开发库...
echo 版本: %SDL2_VERSION%
echo.

:: 创建临时目录
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

:: 使用PowerShell下载SDL2
echo 📥 正在下载SDL2开发库，请稍候...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%SDL2_URL%' -OutFile '%TEMP_DIR%\SDL2.zip' -UserAgent 'Mozilla/5.0'}" >nul 2>&1

if not exist "%TEMP_DIR%\SDL2.zip" (
    echo ❌ 下载失败！请检查网络连接
    echo.
    echo 💡 手动下载方法:
    echo    1. 访问: https://www.libsdl.org/download-2.0.php
    echo    2. 下载 "Development Libraries" 中的 MinGW 版本
    echo    3. 解压到 C:\SDL2 目录
    pause
    exit /b 1
)

echo ✅ 下载完成
echo 📂 正在解压到 %SDL2_DIR%...

:: 解压SDL2
powershell -Command "& {Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%TEMP_DIR%\SDL2.zip', '%TEMP_DIR%\extracted')}" >nul 2>&1

:: 查找解压后的SDL2目录并移动到目标位置
for /d %%i in ("%TEMP_DIR%\extracted\SDL2-*") do (
    if exist "%%i" (
        echo 📁 移动文件到 %SDL2_DIR%...
        if exist "%SDL2_DIR%" rmdir /s /q "%SDL2_DIR%"
        move "%%i" "%SDL2_DIR%" >nul
    )
)

:: 清理临时文件
echo 🧹 清理临时文件...
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"

:: 验证安装
if not exist "%SDL2_DIR%\bin\SDL2.dll" (
    echo ❌ SDL2安装失败！
    pause
    exit /b 1
)

echo ✅ SDL2安装成功！

:configure_build
echo.
echo ⚙️ 配置编译环境...

:: 检查GCC编译器
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到GCC编译器
    echo.
    echo 💡 请安装MinGW-w64编译器:
    echo    方法1: 安装TDM-GCC (https://jmeubank.github.io/tdm-gcc/)
    echo    方法2: 安装MSYS2 (https://www.msys2.org/)
    echo    方法3: 安装MinGW-w64 (https://www.mingw-w64.org/)
    pause
    exit /b 1
)

echo ✅ 找到GCC编译器
gcc --version | findstr gcc

:: 检查必需文件
echo 🔍 检查项目文件...
if not exist "simulator_main.c" (
    echo ❌ 缺少 simulator_main.c 文件
    pause
    exit /b 1
)
if not exist "lvgl" (
    echo ❌ 缺少 lvgl 目录
    pause
    exit /b 1
)
if not exist "lvgl_user" (
    echo ❌ 缺少 lvgl_user 目录
    pause
    exit /b 1
)
echo ✅ 项目文件检查完毕

:: 创建Makefile
echo 📝 创建构建配置...
(
echo # LVGL模拟器 Makefile with Direct SDL2 Path
echo.
echo CC = gcc
echo CFLAGS = -std=c99 -Wall -Wextra -O2 -g -Wno-unused-parameter
echo CFLAGS += -I./lvgl -I./ -I./lvgl_user
echo CFLAGS += -I%SDL2_DIR%/include/SDL2 -I%SDL2_DIR%/include
echo.
echo LDFLAGS = -L%SDL2_DIR%/lib -lmingw32 -lSDL2main -lSDL2 -lm
echo.
echo # LVGL核心源文件
echo LVGL_CORE := $^(wildcard lvgl/src/core/*.c^)
echo LVGL_DRAW := $^(wildcard lvgl/src/draw/*.c^)
echo LVGL_DRAW_SW := $^(wildcard lvgl/src/draw/sw/*.c^)
echo LVGL_HAL := $^(wildcard lvgl/src/hal/*.c^)
echo LVGL_MISC := $^(wildcard lvgl/src/misc/*.c^)
echo LVGL_WIDGETS := $^(wildcard lvgl/src/widgets/*.c^)
echo LVGL_FONT := $^(wildcard lvgl/src/font/*.c^)
echo.
echo # LVGL额外组件
echo LVGL_EXTRA_WIDGETS := $^(wildcard lvgl/src/extra/widgets/*/*.c^)
echo LVGL_EXTRA_THEMES := $^(wildcard lvgl/src/extra/themes/*/*.c^)
echo LVGL_EXTRA_LAYOUTS := $^(wildcard lvgl/src/extra/layouts/*/*.c^)
echo.
echo # UI应用源文件
echo UI_SOURCES := $^(wildcard lvgl_user/ui_app/*.c^)
echo UI_SOURCES += $^(wildcard lvgl_user/ui_app/screens/*.c^)
echo UI_SOURCES += $^(wildcard lvgl_user/ui_app/components/*.c^)
echo UI_SOURCES += $^(wildcard lvgl_user/ui_app/fonts/*.c^)
echo.
echo # 主模拟器文件
echo MAIN_SRC = simulator_main.c
echo.
echo # 所有源文件
echo SOURCES = $^(LVGL_CORE^) $^(LVGL_DRAW^) $^(LVGL_DRAW_SW^) $^(LVGL_HAL^) $^(LVGL_MISC^)
echo SOURCES += $^(LVGL_WIDGETS^) $^(LVGL_FONT^) $^(LVGL_EXTRA_WIDGETS^) $^(LVGL_EXTRA_THEMES^) $^(LVGL_EXTRA_LAYOUTS^)
echo SOURCES += $^(UI_SOURCES^) $^(MAIN_SRC^)
echo.
echo # 目标文件
echo OBJECTS = $^(SOURCES:.c=.o^)
echo.
echo # 可执行文件名
echo TARGET = lvgl_simulator.exe
echo.
echo .PHONY: all clean run
echo.
echo all: $^(TARGET^)
echo 	@echo ✅ 构建完成! 运行: ./$$^(TARGET^)
echo.
echo $^(TARGET^): $^(OBJECTS^)
echo 	@echo 🔗 链接可执行文件...
echo 	$^(CC^) $^(OBJECTS^) -o $$@ $^(LDFLAGS^)
echo.
echo %%.o: %%.c
echo 	@echo 📝 编译: $$^<
echo 	$^(CC^) $^(CFLAGS^) -c $$^< -o $$@
echo.
echo clean:
echo 	@echo 🧹 清理构建文件...
echo 	del /Q $^(TARGET^) 2^>nul ^|^| true
echo 	for /r . %%%%i in ^(*.o^) do del "%%%%i" 2^>nul ^|^| true
echo.
echo run: $^(TARGET^)
echo 	@echo 🚀 启动模拟器...
echo 	set PATH=%SDL2_DIR%\bin;%%%%PATH%%%%
echo 	./$$^(TARGET^)
) > Makefile.direct

:: 使用模拟器配置
echo ⚙️ 配置LVGL...
if exist "lvgl\lv_conf.h" (
    copy "lvgl\lv_conf.h" "lvgl\lv_conf.h.backup" >nul 2>&1
)

if exist "lv_conf_simulator.h" (
    copy "lv_conf_simulator.h" "lvgl\lv_conf.h" >nul 2>&1
    echo ✅ 使用模拟器配置文件
)

:: 添加SDL2到当前会话PATH
set "PATH=%SDL2_DIR%\bin;%PATH%"

echo.
echo 🏗️ 开始编译...
echo.

:: 清理之前的构建
del /Q *.o lvgl_simulator.exe 2>nul >nul

:: 开始构建
make -f Makefile.direct clean 2>nul
make -f Makefile.direct

if %errorlevel% equ 0 (
    echo.
    echo 🎉 编译成功！
    echo 🚀 正在启动模拟器...
    echo 💡 操作说明:
    echo    - 鼠标左键: 模拟触摸点击
    echo    - ESC键: 退出模拟器
    echo.
    
    :: 启动模拟器
    start lvgl_simulator.exe
    
    echo ✅ 模拟器已启动！
) else (
    echo.
    echo ❌ 编译失败！请检查错误信息
    echo.
    echo 💡 常见问题解决:
    echo    1. 确保所有源文件存在
    echo    2. 检查GCC编译器是否正确安装
    echo    3. 尝试手动编译单个文件测试
    pause
    exit /b 1
)

echo.
echo ✅ 所有操作完成！
echo 💡 下次可以直接运行: lvgl_simulator.exe
echo.
pause 