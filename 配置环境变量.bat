@echo off
echo 正在检查并配置环境变量...

:: 检查MSYS2是否已安装
if not exist "C:\msys64\mingw64\bin\gcc.exe" (
    echo ❌ 未找到MSYS2安装，请先安装MSYS2
    echo 下载地址: https://www.msys2.org/
    pause
    exit /b 1
)

:: 将MSYS2路径添加到当前会话的PATH
set "MSYS2_PATH=C:\msys64\mingw64\bin"
echo 当前会话PATH已添加: %MSYS2_PATH%

:: 永久添加到系统PATH（需要管理员权限）
echo.
echo 检测编译环境:
echo.

:: 测试GCC
"%MSYS2_PATH%\gcc.exe" --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ GCC编译器: 已安装
    "%MSYS2_PATH%\gcc.exe" --version | findstr "gcc"
) else (
    echo ❌ GCC编译器: 未找到
)

:: 测试SDL2
"%MSYS2_PATH%\pkg-config.exe" --exists sdl2 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SDL2开发库: 已安装
    "%MSYS2_PATH%\pkg-config.exe" --modversion sdl2
) else (
    echo ❌ SDL2开发库: 未找到
    echo.
    echo 请在MSYS2 MINGW64终端中执行:
    echo   pacman -S mingw-w64-x86_64-SDL2
)

echo.
echo 💡 如需永久添加到系统PATH，请手动操作:
echo    1. 按Win+R，输入sysdm.cpl，按回车
echo    2. 点击"环境变量"按钮
echo    3. 在"系统变量"中找到"Path"，点击"编辑"
echo    4. 点击"新建"，输入: C:\msys64\mingw64\bin
echo    5. 点击"确定"保存
echo.

:: 临时设置PATH，使本次会话生效
set "PATH=%MSYS2_PATH%;%PATH%"
echo 当前终端会话已设置PATH，可以直接运行构建脚本

pause 