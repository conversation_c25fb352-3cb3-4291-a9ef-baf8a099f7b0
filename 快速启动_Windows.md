# Windows系统快速启动指南

## 🚀 一键启动（推荐）

**方法1：双击运行**
1. 双击运行 `build_simulator.bat`
2. 按照提示安装依赖
3. 等待自动编译完成
4. 模拟器将自动启动

## 🔧 环境准备

### 选择开发环境

#### 方案A：MSYS2 + MinGW-w64（推荐）

1. **下载安装MSYS2**
   - 访问：https://www.msys2.org/
   - 下载并安装最新版本

2. **安装开发工具**
   ```bash
   # 在MSYS2终端中执行
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-SDL2  
   pacman -S make
   ```

3. **添加到系统PATH**
   - 将 `C:\msys64\mingw64\bin` 添加到系统环境变量PATH

#### 方案B：TDM-GCC（简单）

1. **下载安装TDM-GCC**
   - 访问：https://jmeubank.github.io/tdm-gcc/
   - 下载并安装（会自动添加到PATH）

2. **手动安装SDL2**
   - 下载SDL2开发库：https://www.libsdl.org/download-2.0.php
   - 选择"Development Libraries" -> "SDL2-devel-x.x.x-mingw.tar.gz"
   - 解压到 `C:\SDL2`
   - 将 `C:\SDL2\bin` 添加到系统PATH

#### 方案C：Visual Studio（专业）

1. **安装Visual Studio 2019/2022**
   - 下载Community版本（免费）
   - 安装时选择"C++桌面开发"工作负载

2. **配置SDL2**
   - 下载SDL2开发库（VC版本）
   - 配置项目包含目录和库目录

## 📋 验证安装

打开命令提示符（CMD）或PowerShell，测试命令：

```cmd
gcc --version
```
应该显示GCC版本信息

```cmd
pkg-config --exists sdl2 && echo "SDL2已安装"
```
或者检查SDL2.dll是否存在：
```cmd
where SDL2.dll
```

## 🏗️ 手动构建（高级用户）

如果自动脚本失败，可以手动执行：

```cmd
# 清理之前的构建
del *.o lvgl_simulator.exe 2>nul

# 编译LVGL核心
gcc -c lvgl/src/core/*.c -I./lvgl -I./
gcc -c lvgl/src/draw/*.c -I./lvgl -I./
gcc -c lvgl/src/draw/sw/*.c -I./lvgl -I./
gcc -c lvgl/src/hal/*.c -I./lvgl -I./
gcc -c lvgl/src/misc/*.c -I./lvgl -I./
gcc -c lvgl/src/widgets/*.c -I./lvgl -I./
gcc -c lvgl/src/font/*.c -I./lvgl -I./

# 编译LVGL额外组件
gcc -c lvgl/src/extra/widgets/*/*.c -I./lvgl -I./
gcc -c lvgl/src/extra/themes/*/*.c -I./lvgl -I./
gcc -c lvgl/src/extra/layouts/*/*.c -I./lvgl -I./

# 编译UI应用
gcc -c lvgl_user/ui_app/*.c -I./lvgl -I./lvgl_user
gcc -c lvgl_user/ui_app/screens/*.c -I./lvgl -I./lvgl_user
gcc -c lvgl_user/ui_app/components/*.c -I./lvgl -I./lvgl_user
gcc -c lvgl_user/ui_app/fonts/*.c -I./lvgl -I./lvgl_user

# 编译主程序
gcc -c simulator_main.c -I./lvgl -I./lvgl_user

# 链接
gcc *.o -o lvgl_simulator.exe -lSDL2main -lSDL2 -lm

# 运行
lvgl_simulator.exe
```

## 🐛 常见问题

### 问题1：'gcc' 不是内部或外部命令
**解决方案：**
- 确保安装了GCC编译器
- 检查PATH环境变量是否包含GCC路径
- 重启命令行窗口

### 问题2：找不到SDL2.h
**解决方案：**
```cmd
# 如果使用MSYS2
pacman -S mingw-w64-x86_64-SDL2

# 如果手动安装SDL2
# 确保SDL2解压到C:\SDL2目录
# 检查是否存在 C:\SDL2\include\SDL2\SDL.h
```

### 问题3：找不到SDL2.dll
**解决方案：**
- 将 `C:\SDL2\bin` 或 `C:\msys64\mingw64\bin` 添加到PATH
- 或者将SDL2.dll复制到项目目录

### 问题4：中文显示乱码
**解决方案：**
- 在CMD中运行：`chcp 65001`
- 或使用Windows Terminal

## 🎮 使用说明

- **鼠标左键**：模拟触摸点击
- **鼠标拖拽**：模拟触摸滑动
- **ESC键或关闭窗口**：退出模拟器

## 🔄 更新项目

如果UI代码有更新：
1. 重新运行 `build_simulator.bat`
2. 或者手动执行：`make -f Makefile.simple clean && make -f Makefile.simple`

## 📞 获取帮助

如果遇到问题：
1. 查看错误信息并参考上述常见问题
2. 确保所有依赖都正确安装
3. 尝试在MSYS2终端中构建（更兼容Linux环境）

---

💡 **提示：** 推荐使用MSYS2环境，它提供了最接近Linux的开发体验，兼容性最好。 