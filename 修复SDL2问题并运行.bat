@echo off
chcp 65001 >nul
echo ========================================
echo       修复SDL2问题并运行LVGL模拟器
echo ========================================
echo.

:: 设置SDL2路径
set "SDL2_DIR=C:\SDL2"
set "SDL2_BIN_DIR=%SDL2_DIR%\x86_64-w64-mingw32\bin"
set "SDL2_LIB_DIR=%SDL2_DIR%\x86_64-w64-mingw32\lib"
set "SDL2_INCLUDE_DIR=%SDL2_DIR%\x86_64-w64-mingw32\include"

echo 🔍 检查SDL2安装状态...

:: 检查SDL2是否存在
if not exist "%SDL2_BIN_DIR%\SDL2.dll" (
    echo ❌ 未找到SDL2.dll在: %SDL2_BIN_DIR%
    echo 请运行 "自动安装SDL2并运行.bat" 先安装SDL2
    pause
    exit /b 1
)

echo ✅ 找到SDL2.dll: %SDL2_BIN_DIR%\SDL2.dll

:: 检查编译器
echo 🔍 检查编译器...
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到GCC编译器
    echo 请确保MinGW-w64已安装并添加到PATH
    pause
    exit /b 1
)

echo ✅ 找到GCC编译器
gcc --version | findstr gcc

:: 临时添加SDL2到PATH（仅当前会话）
echo 🔧 临时添加SDL2到PATH...
set "PATH=%SDL2_BIN_DIR%;%PATH%"
echo ✅ SDL2已添加到当前会话PATH

:: 验证SDL2是否可以找到
echo 🔍 验证SDL2库...
where SDL2.dll >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ SDL2.dll现在可以找到了
) else (
    echo ❌ SDL2.dll仍然无法找到
    pause
    exit /b 1
)

:: 检查项目文件
echo 🔍 检查项目文件...
if not exist "simulator_main.c" (
    echo ❌ 缺少 simulator_main.c 文件
    pause
    exit /b 1
)
if not exist "lvgl" (
    echo ❌ 缺少 lvgl 目录
    pause
    exit /b 1
)
if not exist "lvgl_user" (
    echo ❌ 缺少 lvgl_user 目录
    pause
    exit /b 1
)
echo ✅ 项目文件检查完毕

:: 复制SDL2.dll到当前目录（确保运行时能找到）
echo 📋 复制SDL2.dll到当前目录...
copy "%SDL2_BIN_DIR%\SDL2.dll" "." >nul 2>&1
if exist "SDL2.dll" (
    echo ✅ SDL2.dll已复制到当前目录
) else (
    echo ⚠️ 无法复制SDL2.dll，但会尝试继续
)

:: 使用模拟器配置
echo ⚙️ 配置LVGL...
if exist "lvgl\lv_conf.h" (
    copy "lvgl\lv_conf.h" "lvgl\lv_conf.h.backup" >nul 2>&1
)

if exist "lv_conf_simulator.h" (
    copy "lv_conf_simulator.h" "lvgl\lv_conf.h" >nul 2>&1
    echo ✅ 使用模拟器配置文件
)

echo.
echo 🏗️ 开始编译...
echo.

:: 直接使用GCC编译，不依赖make
echo 📝 使用直接编译方式...

:: 清理旧文件
del /Q lvgl_simulator.exe 2>nul >nul
del /Q *.o 2>nul >nul

:: 设置编译参数
set "CFLAGS=-std=c99 -Wall -Wextra -O2 -g -Wno-unused-parameter -Wno-unused-function"
set "INCLUDES=-I./lvgl -I./ -I./lvgl_user -I%SDL2_INCLUDE_DIR%/SDL2 -I%SDL2_INCLUDE_DIR%"
set "LDFLAGS=-L%SDL2_LIB_DIR% -lmingw32 -lSDL2main -lSDL2 -lm"

:: 编译所有源文件
echo 📝 编译LVGL核心文件...
gcc %CFLAGS% %INCLUDES% -c lvgl/src/core/*.c
gcc %CFLAGS% %INCLUDES% -c lvgl/src/draw/*.c
gcc %CFLAGS% %INCLUDES% -c lvgl/src/draw/sw/*.c
gcc %CFLAGS% %INCLUDES% -c lvgl/src/hal/*.c
gcc %CFLAGS% %INCLUDES% -c lvgl/src/misc/*.c
gcc %CFLAGS% %INCLUDES% -c lvgl/src/widgets/*.c
gcc %CFLAGS% %INCLUDES% -c lvgl/src/font/*.c

echo 📝 编译用户文件...
gcc %CFLAGS% %INCLUDES% -c lvgl_user/*.c 2>nul
gcc %CFLAGS% %INCLUDES% -c lvgl_user/ui_app/*.c 2>nul

echo 📝 编译主文件...
gcc %CFLAGS% %INCLUDES% -c simulator_main.c

echo 🔗 链接可执行文件...
gcc *.o -o lvgl_simulator.exe %LDFLAGS%

if %errorlevel% equ 0 (
    echo.
    echo 🎉 编译成功！
    echo.
    echo 📋 文件检查:
    if exist "lvgl_simulator.exe" (
        echo ✅ 可执行文件: lvgl_simulator.exe
        dir lvgl_simulator.exe
    )
    if exist "SDL2.dll" (
        echo ✅ SDL2库: SDL2.dll
    )
    
    echo.
    echo 🚀 正在启动模拟器...
    echo 💡 操作说明:
    echo    - 鼠标左键: 模拟触摸点击
    echo    - 鼠标拖拽: 模拟滑动
    echo    - ESC键: 退出模拟器
    echo.
    
    :: 启动模拟器
    start lvgl_simulator.exe
    
    echo ✅ 模拟器已启动！
    echo.
    echo 💡 下次运行可以直接双击: lvgl_simulator.exe
    
) else (
    echo.
    echo ❌ 编译失败！
    echo.
    echo 🔧 故障排除建议:
    echo    1. 检查所有源文件是否存在
    echo    2. 确认GCC编译器版本兼容
    echo    3. 检查SDL2路径是否正确
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 所有操作完成！
pause
